import React, { useEffect } from 'react'
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

function Scene() {

    useEffect(() => {
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);


        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshStandardMaterial({ color: 'white' });
        const cube = new THREE.Mesh(geometry, material);
        scene.add(cube);

        camera.position.z = 2;

        let ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);


        const rgbeLoader = new RGBELoader();
        rgbeLoader.load('https://dl.polyhaven.org/file/ph-assets/HDRIs/hdr/1k/park_music_stage_1k.hdr', function(texture) {
            texture.mapping = THREE.EquirectangularReflectionMapping;
            scene.background = texture;
            scene.environment = texture;
        })

        const loader = new GLTFLoader();
        loader.load('the_parade_armour_of_king_erik_xiv_of_sweden.glb', function(gltf){
            const model = gltf.scene;

            // Calculate the bounding box of the model
            const box = new THREE.Box3().setFromObject(model);
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());

            // Center the model by moving it to the origin
            model.position.x = -center.x;
            model.position.y = -center.y;
            model.position.z = -center.z;

            // Optional: Scale the model to fit nicely in the viewport
            const maxDim = Math.max(size.x, size.y, size.z);
            const scale = 2 / maxDim; // Adjust this value to make model bigger/smaller
            model.scale.setScalar(scale);

            scene.add(model);
        })
        

        const canvas = document.querySelector('canvas');
        const renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.DampingFactor = 0.25;
        controls.enableZoom = true;

        window.addEventListener('resize', function(){
            camera.aspect = this.window.innerWidth / this.window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(this.window.innerWidth, window.innerHeight)
        })

        function animate() {
            renderer.render(scene, camera);
            // cube.rotation.x += 0.01;
            // cube.rotation.y += 0.01;
            controls.update();
            requestAnimationFrame(animate);
        }
       
        animate();
        return () => {
            renderer.dispose();
        }
    }, [])
    return (
        <div>
            <canvas></canvas>
        </div>
    )
}

export default Scene
